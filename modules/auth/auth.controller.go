package auth

import (
	"net/http"
	"net/url"
	"strings"

	"gitlab.finema.co/finema/csp/csp-api/consts"
	"gitlab.finema.co/finema/csp/csp-api/helpers"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type AuthController struct {
}

func (m AuthController) Logout(c core.IHTTPContext) error {
	authSvc := services.NewAuthService(c)
	err := authSvc.Logout(c.GetUser().Data["token"])
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.<PERSON>ontent(http.StatusNoContent)
}

func (m AuthController) Login(c core.IHTTPContext) error {
	panic("not implemented")
}
